package org.befun.core;

import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.configuration.ApplicationProperties;
import org.befun.core.rest.ResourceAccessDeniedHandler;
import org.befun.core.rest.ResponseExceptionHandler;
import org.befun.core.security.LegacyAuthTokenFilter;
import org.befun.core.security.OpenApiTokenFilter;
import org.befun.core.utils.JsonHelper;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableWebMvc
@EnableWebSecurity
//@EntityScan("org.befun.core.entity")
@ComponentScan({"org.befun.core.service"})
@EnableGlobalMethodSecurity(prePostEnabled = true)
@EnableConfigurationProperties(ApplicationProperties.class)
@Order(90)
public class AutoConfiguration extends WebSecurityConfigurerAdapter implements WebMvcConfigurer {

    @Autowired
    private ApplicationProperties applicationProperties;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedMethods("HEAD", "GET", "PUT", "POST", "DELETE", "PATCH");
    }

    @Bean("staticJsonHelper")
    public JsonHelper jsonHelper(ObjectMapper objectMapper){
        return new JsonHelper(objectMapper);
    }

    @Bean
    public ResponseExceptionHandler exceptionAdvice() {
        return new ResponseExceptionHandler();
    }

    @Bean
    @ConditionalOnMissingBean
    public LegacyAuthTokenFilter legacyTokenFilter() {
        return new LegacyAuthTokenFilter();
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "befun.server", name = "enable-open-api-filter", havingValue = "true")
    public OpenApiTokenFilter openApiTokenFilter() {
        return new OpenApiTokenFilter();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                .cors()
                .and().csrf().disable()
                .anonymous()
                .and().exceptionHandling().accessDeniedHandler(new ResourceAccessDeniedHandler());

        http.addFilterBefore(legacyTokenFilter(), BasicAuthenticationFilter.class);
        if (applicationProperties.getEnableOpenApiFilter()) {
            http.addFilterAfter(openApiTokenFilter(), LegacyAuthTokenFilter.class);
        }
        /**
         * Security默认的登出地址为：/logout，在登出后会进行如下操作：
         * 移除HttpSession
         * 清空配置的RememberMe认证
         * 清空SecurityContextHolder`
         * 重定向到/login?logout
         **/
        http.logout().disable();
    }

    @Bean
    public ModelMapper modelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setSkipNullEnabled(true)
                .setCollectionsMergeEnabled(false)
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setFieldMatchingEnabled(true)
                .setFieldAccessLevel(org.modelmapper.config.Configuration.AccessLevel.PACKAGE_PRIVATE);
        return modelMapper;
    }

    @ConditionalOnProperty(prefix = "befun.id", name = "enable-long-to-string", havingValue = "true")
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        log.info("enable long to string");
        return builder -> {
            SimpleModule module = new SimpleModule();

            module.setSerializerModifier(new BeanSerializerModifier() {
                @Override
                public List<BeanPropertyWriter> changeProperties(
                        SerializationConfig config,
                        BeanDescription beanDesc,
                        List<BeanPropertyWriter> beanProperties) {
                    for (BeanPropertyWriter writer : beanProperties) {
                        // 只处理 Long 类型，且字段名是 "id"
                        if (writer.getType().isTypeOrSubTypeOf(Long.class)
                                && (writer.getName().endsWith("id")) || writer.getName().endsWith("Id"))  {
                            writer.assignSerializer(ToStringSerializer.instance);
                        }
                    }
                    return beanProperties;
                }
            });

            builder.modules(module);
        };
    }
}

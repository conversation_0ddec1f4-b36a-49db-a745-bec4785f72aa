package org.befun.extension.advice;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@RestControllerAdvice
public class ApiResponseVersionAdvice implements ResponseBodyAdvice<Object> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    private static final String API_VERSION_HEADER = "api-version";
    private static final String TARGET_API_VERSION = "v2";
    private static final List<String> ID_FIELD_NAMES = List.of("id", "Id", "ids", "Ids");

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // Apply to all responses
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {

        ServletServerHttpRequest servletRequest = (ServletServerHttpRequest) request;
        HttpServletRequest httpServletRequest = servletRequest.getServletRequest();
        String apiVersion = httpServletRequest.getHeader(API_VERSION_HEADER);

        if (TARGET_API_VERSION.equals(apiVersion)) {
            if (body == null) {
                return null;
            }
            // Convert body to a mutable structure (Map/List) to modify it
            Object flexibleBody = objectMapper.convertValue(body, Object.class);
            convertIdsToString(flexibleBody);
            return flexibleBody;
        }

        return body;
    }

    private void convertIdsToString(Object obj) {
        if (obj instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) obj;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                if (ID_FIELD_NAMES.stream().anyMatch(key::endsWith) && value instanceof Number) {
                    entry.setValue(value.toString());
                } else {
                    convertIdsToString(value);
                }
            }
        } else if (obj instanceof Collection) {
            Collection<?> collection = (Collection<?>) obj;
            for (Object item : collection) {
                convertIdsToString(item);
            }
        }
    }
}
